import Link from "next/link";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Facebook,
  Instagram,
  Twitter,
} from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-[var(--primary-text)] text-white">
      <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Company Info */}
          <div className="space-y-4">
            <Link href="/" className="text-2xl font-bold">
              alexis
              <span className="text-[var(--secondary-brand)]">tiling</span>
            </Link>
            <p className="text-gray-300">
              Professional tiling services with over 10 years of experience.
              Quality craftsmanship guaranteed.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
              >
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/"
                  className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/services"
                  className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
                >
                  Services
                </Link>
              </li>
              <li>
                <Link
                  href="/gallery"
                  className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
                >
                  Gallery
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
                >
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Our Services</h3>
            <ul className="space-y-2">
              <li className="text-gray-300">Floor Tiling</li>
              <li className="text-gray-300">Wall Tiling</li>
              <li className="text-gray-300">Bathroom Renovation</li>
              <li className="text-gray-300">Kitchen Backsplash</li>
              <li className="text-gray-300">Outdoor Tiling</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-[var(--secondary-brand)]" />
                <span className="text-gray-300">+49 ************</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-[var(--secondary-brand)]" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-[var(--secondary-brand)]" />
                <span className="text-gray-300">Berlin, Germany</span>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 text-[var(--secondary-brand)]" />
                <span className="text-gray-300">Mon-Fri: 8AM-6PM</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 border-t border-gray-700 pt-8">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <p className="text-gray-300">
              © 2024 Alexis Tiling. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link
                href="/privacy"
                className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-gray-300 transition-colors hover:text-[var(--secondary-brand)]"
              >
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
