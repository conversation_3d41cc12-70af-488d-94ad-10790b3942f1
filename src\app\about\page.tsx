import { CheckCircle, Award, Users, Clock, Target, Heart, Shield } from "lucide-react";
import SectionHeader from "@/components/shared/section-header";
import CTASection from "@/components/shared/cta-section";
import Footer from "@/components/shared/footer";

export default function AboutPage() {
  const stats = [
    { icon: Award, label: "Years Experience", value: "10+" },
    { icon: Users, label: "Happy Clients", value: "500+" },
    { icon: CheckCircle, label: "Projects Completed", value: "1000+" },
    { icon: Clock, label: "Average Response", value: "24h" },
  ];

  const values = [
    {
      icon: Target,
      title: "Precision",
      description: "Every tile placed with meticulous attention to detail and perfect alignment."
    },
    {
      icon: Heart,
      title: "Passion",
      description: "We love what we do and it shows in the quality of our craftsmanship."
    },
    {
      icon: Shield,
      title: "Reliability",
      description: "Dependable service with guaranteed satisfaction on every project."
    }
  ];

  return (
    <div className="min-h-screen bg-[var(--primary-bg)] pt-16">
      {/* Hero Section */}
      <section className="section-padding gradient-hero">
        <div className="container-custom">
          <div className="text-center">
            <SectionHeader
              subtitle="About Alexis Tiling"
              title="Crafting Beautiful Spaces Since 2014"
              description="We are a family-owned tiling business dedicated to transforming homes and commercial spaces across Berlin and Brandenburg with exceptional craftsmanship and personalized service."
            />
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="section-padding bg-[var(--card-bg)]">
        <div className="container-custom">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-[var(--primary-text)]">Our Story</h2>
              <div className="space-y-4 text-[var(--secondary-text)]">
                <p>
                  Founded in 2014 by Alexis, our company began with a simple mission: to provide 
                  exceptional tiling services that transform ordinary spaces into extraordinary ones. 
                  What started as a one-person operation has grown into a trusted team of skilled 
                  craftsmen serving the Berlin and Brandenburg region.
                </p>
                <p>
                  Over the years, we've built our reputation on quality workmanship, reliable service, 
                  and genuine care for our customers. From small bathroom renovations to large 
                  commercial projects, we approach every job with the same level of dedication 
                  and attention to detail.
                </p>
                <p>
                  Today, we're proud to be one of the most trusted tiling contractors in the region, 
                  with hundreds of satisfied customers and a portfolio of beautiful, lasting installations.
                </p>
              </div>
            </div>
            <div className="relative">
              <img
                src="/hero.jpg"
                alt="Alexis at work"
                className="rounded-2xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-padding bg-[var(--secondary-bg)]">
        <div className="container-custom">
          <SectionHeader
            title="Our Achievements"
            description="Numbers that reflect our commitment to excellence and customer satisfaction."
            className="mb-12"
          />
          <div className="grid grid-cols-2 gap-6 lg:grid-cols-4">
            {stats.map((stat, index) => (
              <div key={index} className="card-base text-center">
                <stat.icon className="mx-auto mb-4 h-12 w-12 text-[var(--secondary-brand)]" />
                <div className="text-3xl font-bold text-[var(--primary-text)]">{stat.value}</div>
                <div className="text-[var(--secondary-text)]">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="section-padding bg-[var(--card-bg)]">
        <div className="container-custom">
          <SectionHeader
            title="Our Values"
            description="The principles that guide everything we do."
            className="mb-12"
          />
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {values.map((value, index) => (
              <div key={index} className="card-base text-center">
                <value.icon className="mx-auto mb-4 h-12 w-12 text-[var(--secondary-brand)]" />
                <h3 className="mb-3 text-xl font-semibold text-[var(--primary-text)]">
                  {value.title}
                </h3>
                <p className="text-[var(--secondary-text)]">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="section-padding bg-[var(--secondary-bg)]">
        <div className="container-custom">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
            <div>
              <h2 className="mb-6 text-3xl font-bold text-[var(--primary-text)]">
                Why Choose Alexis Tiling?
              </h2>
              <div className="space-y-4">
                {[
                  "Licensed and insured professionals",
                  "Free detailed quotes and consultations",
                  "High-quality materials from trusted suppliers",
                  "Clean, efficient work practices",
                  "Competitive pricing with no hidden costs",
                  "100% satisfaction guarantee",
                  "Ongoing support and maintenance advice"
                ].map((item, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="mt-1 h-5 w-5 text-[var(--secondary-brand)]" />
                    <span className="text-[var(--secondary-text)]">{item}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <img
                src="/hero.jpg"
                alt="Quality tiling work"
                className="rounded-2xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      <CTASection />
      <Footer />
    </div>
  );
}
