import { Check<PERSON>ircle, Award, Users, Clock } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export const AboutPreview = () => {
  const stats = [
    { icon: Award, label: "Years Experience", value: "10+" },
    { icon: Users, label: "Happy Clients", value: "500+" },
    { icon: CheckCircle, label: "Projects Completed", value: "1000+" },
    { icon: Clock, label: "Response Time", value: "24h" },
  ];

  return (
    <section className="bg-[var(--card-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
      <div className="mx-auto max-w-7xl">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          {/* Content */}
          <div className="space-y-6">
            <div>
              <p className="mb-2 text-sm font-semibold tracking-wider text-[var(--secondary-brand)] uppercase">
                About Alexis Tiling
              </p>
              <h2 className="mb-4 text-3xl font-bold text-[var(--primary-text)] sm:text-4xl lg:text-5xl">
                Professional Tiling Services You Can Trust
              </h2>
              <p className="mb-6 text-lg text-[var(--secondary-text)] sm:text-xl">
                With over a decade of experience in the tiling industry, we
                specialize in transforming spaces with precision, quality, and
                attention to detail. From residential bathrooms to commercial
                spaces, we deliver excellence in every project.
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="mt-1 h-5 w-5 text-[var(--secondary-brand)]" />
                <div>
                  <h4 className="font-semibold text-[var(--primary-text)]">
                    Quality Materials
                  </h4>
                  <p className="text-[var(--secondary-text)]">
                    We use only premium tiles and materials from trusted
                    suppliers
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="mt-1 h-5 w-5 text-[var(--secondary-brand)]" />
                <div>
                  <h4 className="font-semibold text-[var(--primary-text)]">
                    Expert Craftsmanship
                  </h4>
                  <p className="text-[var(--secondary-text)]">
                    Skilled professionals with years of hands-on experience
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="mt-1 h-5 w-5 text-[var(--secondary-brand)]" />
                <div>
                  <h4 className="font-semibold text-[var(--primary-text)]">
                    Guaranteed Satisfaction
                  </h4>
                  <p className="text-[var(--secondary-text)]">
                    100% satisfaction guarantee on all our work
                  </p>
                </div>
              </div>
            </div>

            <Link href="/about">
              <Button className="bg-[var(--secondary-brand)] text-white hover:bg-[var(--secondary-brand)]/90">
                Learn More About Us
              </Button>
            </Link>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-6">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 text-center shadow-sm"
              >
                <stat.icon className="mx-auto mb-3 h-8 w-8 text-[var(--secondary-brand)]" />
                <div className="text-2xl font-bold text-[var(--primary-text)]">
                  {stat.value}
                </div>
                <div className="text-sm text-[var(--secondary-text)]">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
