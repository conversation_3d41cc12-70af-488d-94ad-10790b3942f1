import {
  Home,
  Hammer,
  Wrench,
  CheckCircle,
  Bath,
  ChefHat,
  Building,
  Palette,
} from "lucide-react";
import SectionHeader from "@/components/shared/section-header";
import CTASection from "@/components/shared/cta-section";
import Footer from "@/components/shared/footer";
import { Button } from "@/components/ui/button";

export default function ServicesPage() {
  const services = [
    {
      icon: Bath,
      title: "Bathroom Tiling",
      description:
        "Complete bathroom renovations with waterproof tiling solutions for walls and floors.",
      features: [
        "Waterproof installation",
        "Anti-slip flooring",
        "Custom designs",
        "Grout sealing",
      ],
      price: "From €45/m²",
    },
    {
      icon: ChefHat,
      title: "Kitchen Tiling",
      description:
        "Beautiful and functional kitchen backsplashes and floor installations.",
      features: [
        "Heat-resistant materials",
        "Easy-clean surfaces",
        "Custom patterns",
        "Precision cutting",
      ],
      price: "From €40/m²",
    },
    {
      icon: Home,
      title: "Floor Tiling",
      description:
        "Durable and stylish floor tiling for residential and commercial spaces.",
      features: [
        "Various tile types",
        "Underfloor heating compatible",
        "Level installation",
        "Long-lasting finish",
      ],
      price: "From €35/m²",
    },
    {
      icon: Building,
      title: "Commercial Tiling",
      description:
        "Professional tiling services for offices, restaurants, and retail spaces.",
      features: [
        "High-traffic durability",
        "Quick installation",
        "Minimal disruption",
        "Maintenance advice",
      ],
      price: "Contact for quote",
    },
    {
      icon: Wrench,
      title: "Outdoor Tiling",
      description:
        "Weather-resistant tiling for terraces, balconies, and outdoor areas.",
      features: [
        "Frost-resistant tiles",
        "Drainage solutions",
        "Non-slip surfaces",
        "UV protection",
      ],
      price: "From €50/m²",
    },
    {
      icon: Palette,
      title: "Tile Repairs",
      description:
        "Professional repair and replacement services for damaged tiles.",
      features: [
        "Color matching",
        "Quick repairs",
        "Minimal disruption",
        "Warranty included",
      ],
      price: "From €25/repair",
    },
  ];

  const process = [
    {
      step: "01",
      title: "Consultation",
      description:
        "Free on-site consultation to assess your needs and provide detailed quote.",
    },
    {
      step: "02",
      title: "Planning",
      description:
        "Detailed planning including material selection, timeline, and preparation requirements.",
    },
    {
      step: "03",
      title: "Preparation",
      description:
        "Surface preparation, protection of surrounding areas, and material delivery.",
    },
    {
      step: "04",
      title: "Installation",
      description:
        "Professional installation with attention to detail and quality craftsmanship.",
    },
    {
      step: "05",
      title: "Finishing",
      description:
        "Final touches, cleanup, and quality inspection to ensure perfect results.",
    },
  ];

  return (
    <div className="min-h-screen bg-[var(--primary-bg)] pt-16">
      {/* Hero Section */}
      <section className="gradient-hero px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <SectionHeader
            subtitle="Our Services"
            title="Professional Tiling Solutions"
            description="From residential bathrooms to commercial spaces, we provide comprehensive tiling services with exceptional quality and attention to detail."
          />
        </div>
      </section>

      {/* Services Grid */}
      <section className="bg-[var(--card-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {services.map((service, index) => (
              <div
                key={index}
                className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
              >
                <service.icon className="mb-4 h-12 w-12 text-[var(--secondary-brand)]" />
                <h3 className="mb-3 text-xl font-semibold text-[var(--primary-text)]">
                  {service.title}
                </h3>
                <p className="mb-4 text-[var(--secondary-text)]">
                  {service.description}
                </p>
                <ul className="mb-4 space-y-2">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-[var(--secondary-brand)]" />
                      <span className="text-sm text-[var(--secondary-text)]">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
                <div className="mt-auto">
                  <div className="mb-3 text-lg font-semibold text-[var(--primary-brand)]">
                    {service.price}
                  </div>
                  <Button className="w-full bg-[var(--secondary-brand)] text-white hover:bg-[var(--secondary-brand)]/90">
                    Get Quote
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="bg-[var(--secondary-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <SectionHeader
            title="Our Process"
            description="A systematic approach ensuring quality results and customer satisfaction."
            className="mb-12"
          />
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-5">
            {process.map((step, index) => (
              <div key={index} className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-[var(--secondary-brand)] text-2xl font-bold text-white">
                  {step.step}
                </div>
                <h3 className="mb-2 text-lg font-semibold text-[var(--primary-text)]">
                  {step.title}
                </h3>
                <p className="text-sm text-[var(--secondary-text)]">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Our Services */}
      <section className="bg-[var(--card-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
            <div>
              <h2 className="mb-6 text-3xl font-bold text-[var(--primary-text)]">
                Why Choose Our Services?
              </h2>
              <div className="space-y-4">
                {[
                  "10+ years of professional experience",
                  "Licensed and fully insured",
                  "Premium quality materials only",
                  "Competitive and transparent pricing",
                  "Free detailed consultations",
                  "100% satisfaction guarantee",
                  "Clean and efficient work practices",
                  "Ongoing support and maintenance advice",
                ].map((item, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="mt-1 h-5 w-5 text-[var(--secondary-brand)]" />
                    <span className="text-[var(--secondary-text)]">{item}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <img
                src="/hero.jpg"
                alt="Professional tiling service"
                className="rounded-2xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      <CTASection />
      <Footer />
    </div>
  );
}
