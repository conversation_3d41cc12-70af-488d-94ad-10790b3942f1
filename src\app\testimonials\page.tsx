import { Star, Quote, MapPin, Calendar } from "lucide-react";
import SectionHeader from "@/components/shared/section-header";
import CTASection from "@/components/shared/cta-section";
import Footer from "@/components/shared/footer";

export default function TestimonialsPage() {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      location: "Berlin Mitte",
      rating: 5,
      date: "March 2024",
      project: "Bathroom Renovation",
      text: "Exceptional work! <PERSON> transformed our bathroom completely. The attention to detail and quality of work exceeded our expectations. The team was professional, punctual, and left everything spotless. Highly recommended!",
      image: "/hero.jpg",
    },
    {
      id: 2,
      name: "<PERSON>",
      location: "Brandenburg",
      rating: 5,
      date: "February 2024",
      project: "Kitchen Backsplash",
      text: "Professional, punctual, and precise. The kitchen backsplash looks amazing and the cleanup was thorough. <PERSON> provided great advice on tile selection and the installation was flawless. <PERSON> definitely hire again for future projects.",
      image: "/hero.jpg",
    },
    {
      id: 3,
      name: "<PERSON>",
      location: "Berlin Charlottenburg",
      rating: 5,
      date: "January 2024",
      project: "Outdoor Terrace",
      text: "Outstanding service from start to finish. Great communication, fair pricing, and beautiful results. Our terrace looks incredible and has withstood the winter weather perfectly. The non-slip tiles were exactly what we needed.",
      image: "/hero.jpg",
    },
    {
      id: 4,
      name: "<PERSON> <PERSON>",
      location: "Berlin Kreuzberg",
      rating: 5,
      date: "December 2023",
      project: "Commercial Restaurant Floor",
      text: "As a restaurant owner, I needed durable flooring that could handle heavy traffic. <PERSON> delivered exactly what we needed. The installation was completed quickly with minimal disruption to our business. Excellent work!",
      image: "/hero.jpg",
    },
    {
      id: 5,
      name: "Sarah Johnson",
      location: "Berlin Prenzlauer Berg",
      rating: 5,
      date: "November 2023",
      project: "Master Bathroom",
      text: "We're absolutely thrilled with our new bathroom! The marble tiles are gorgeous and the heated floor is a luxury we enjoy every day. Alexis was patient with our many questions and delivered exactly what we envisioned.",
      image: "/hero.jpg",
    },
    {
      id: 6,
      name: "Robert Klein",
      location: "Brandenburg",
      rating: 5,
      date: "October 2023",
      project: "Living Room Floor",
      text: "The large format tiles in our living room look stunning. The installation was precise and the team worked efficiently. Great value for money and the quality is outstanding. Would recommend to anyone looking for professional tiling.",
      image: "/hero.jpg",
    },
    {
      id: 7,
      name: "Lisa Braun",
      location: "Berlin Wilmersdorf",
      rating: 5,
      date: "September 2023",
      project: "Guest Bathroom",
      text: "Small bathroom, big transformation! Alexis made excellent suggestions for maximizing the space and the tile choices were perfect. The work was completed on time and within budget. Very satisfied with the results.",
      image: "/hero.jpg",
    },
    {
      id: 8,
      name: "David Fischer",
      location: "Berlin Friedrichshain",
      rating: 5,
      date: "August 2023",
      project: "Kitchen Floor & Backsplash",
      text: "Complete kitchen tiling project handled with professionalism and skill. The geometric pattern backsplash is exactly what we wanted and the floor tiles are perfect for our busy family kitchen. Highly recommend!",
      image: "/hero.jpg",
    },
  ];

  const stats = [
    { value: "500+", label: "Happy Customers" },
    { value: "4.9/5", label: "Average Rating" },
    { value: "98%", label: "Satisfaction Rate" },
    { value: "1000+", label: "Projects Completed" },
  ];

  return (
    <div className="min-h-screen bg-[var(--primary-bg)] pt-16">
      {/* Hero Section */}
      <section className="gradient-hero px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <SectionHeader
            subtitle="Customer Reviews"
            title="What Our Clients Say"
            description="Read genuine reviews from our satisfied customers across Berlin and Brandenburg. Their success stories speak to our commitment to quality and service excellence."
          />
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-[var(--card-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-2 gap-6 lg:grid-cols-4">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-[var(--primary-brand)] lg:text-4xl">
                  {stat.value}
                </div>
                <div className="text-[var(--secondary-text)]">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Grid */}
      <section className="bg-[var(--secondary-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="rounded-xl border border-l-4 border-[var(--border-color)] border-l-[var(--secondary-brand)] bg-[var(--card-bg)] p-6 shadow-sm transition-all duration-300 hover:shadow-md"
              >
                <div className="mb-4 flex items-center justify-between">
                  <div className="flex">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  <Quote className="h-6 w-6 text-[var(--secondary-brand)] opacity-50" />
                </div>

                <p className="mb-4 text-[var(--secondary-text)] italic">
                  "{testimonial.text}"
                </p>

                <div className="border-t border-[var(--border-color)] pt-4">
                  <div className="font-semibold text-[var(--primary-text)]">
                    {testimonial.name}
                  </div>
                  <div className="mb-2 flex items-center space-x-4 text-sm text-[var(--muted-text)]">
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>{testimonial.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{testimonial.date}</span>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-[var(--secondary-brand)]">
                    {testimonial.project}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Review */}
      <section className="bg-[var(--card-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="mx-auto max-w-4xl text-center">
            <Quote className="mx-auto mb-6 h-12 w-12 text-[var(--secondary-brand)] opacity-50" />
            <blockquote className="mb-6 text-xl text-[var(--secondary-text)] italic lg:text-2xl">
              "Working with Alexis was an absolute pleasure. From the initial
              consultation to the final cleanup, everything was handled with
              professionalism and care. The quality of work is exceptional and
              our new bathroom is exactly what we dreamed of. I wouldn't
              hesitate to recommend Alexis Tiling to anyone looking for
              top-quality tiling services."
            </blockquote>
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className="mx-auto mb-4 h-6 w-6 fill-yellow-400 text-yellow-400"
                />
              ))}
            </div>
            <cite className="text-lg font-semibold text-[var(--primary-text)]">
              - Maria Schmidt, Berlin Mitte
            </cite>
          </div>
        </div>
      </section>

      <CTASection
        title="Ready to Join Our Happy Customers?"
        description="Experience the same quality and service that our customers rave about. Get your free quote today!"
      />
      <Footer />
    </div>
  );
}
