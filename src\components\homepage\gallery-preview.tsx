import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import SectionHeader from "@/components/shared/section-header";

export const GalleryPreview = () => {
  const projects = [
    {
      id: 1,
      title: "Modern Bathroom Renovation",
      category: "Bathroom",
      image: "/hero.jpg", // Using placeholder for now
    },
    {
      id: 2,
      title: "Kitchen Backsplash Design",
      category: "Kitchen",
      image: "/hero.jpg", // Using placeholder for now
    },
    {
      id: 3,
      title: "Outdoor Terrace Tiling",
      category: "Outdoor",
      image: "/hero.jpg", // Using placeholder for now
    },
    {
      id: 4,
      title: "Commercial Floor Installation",
      category: "Commercial",
      image: "/hero.jpg", // Using placeholder for now
    },
    {
      id: 5,
      title: "Luxury Hotel Bathroom",
      category: "Commercial",
      image: "/hero.jpg", // Using placeholder for now
    },
    {
      id: 6,
      title: "Residential Floor Tiling",
      category: "Residential",
      image: "/hero.jpg", // Using placeholder for now
    },
  ];

  return (
    <section className="bg-[var(--secondary-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
      <div className="mx-auto max-w-7xl">
        <SectionHeader
          subtitle="Our Work"
          title="Recent Projects"
          description="Take a look at some of our recent tiling projects and see the quality craftsmanship we deliver."
          className="mb-12"
        />

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {projects.map((project) => (
            <div
              key={project.id}
              className="group overflow-hidden rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-0 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
            >
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="h-64 w-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                <div className="absolute right-4 bottom-4 left-4 translate-y-4 transform text-white opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100">
                  <span className="mb-1 inline-block rounded-full bg-[var(--secondary-brand)] px-2 py-1 text-xs font-medium">
                    {project.category}
                  </span>
                  <h3 className="text-lg font-semibold">{project.title}</h3>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link href="/gallery">
            <Button className="flex items-center gap-2 bg-[var(--secondary-brand)] text-white hover:bg-[var(--secondary-brand)]/90">
              View All Projects
              <ArrowRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};
