import { CheckCircle, Hammer, Home, Wrench } from "lucide-react";

export const NavMenuData = [
  { id: 1, name: "Home", href: "/" },
  { id: 2, name: "About", href: "/about" },
  { id: 3, name: "Services", href: "/services" },
  { id: 4, name: "Gallery", href: "/gallery" },
  { id: 5, name: "Testimonials", href: "/testimonials" },
  { id: 6, name: "Contact", href: "/contact" },
];

// HomePage Services Cards Data
export const HomePageService = [
  {
    icon: Home,
    title: "Floor & Wall Installation",
    description:
      "Professional tile installation for floors and walls with precision and quality craftsmanship",
  },
  {
    icon: Hammer,
    title: "Complete Renovations",
    description:
      "Full bathroom and kitchen renovations from design to completion",
  },
  {
    icon: Wrench,
    title: "Outdoor Solutions",
    description:
      "Weather-resistant terrace and outdoor tiling with drainage solutions",
  },
  {
    icon: CheckCircle,
    title: "Repair Services",
    description: "Quick and reliable tile repair and replacement services",
  },
];

// Company Information
export const CompanyInfo = {
  name: "<PERSON> Tiling",
  phone: "+49 ************",
  email: "<EMAIL>",
  address: "Berlin, Germany",
  serviceArea: "Berlin & Brandenburg",
  established: "2014",
  experience: "10+",
  projectsCompleted: "1000+",
  happyClients: "500+",
};
