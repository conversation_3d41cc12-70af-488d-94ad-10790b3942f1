import { <PERSON><PERSON>ircle, Hammer, Home, Wrench } from "lucide-react";

export const NavMenuData = [
  { id: 1, name: "Home", href: "/" },
  { id: 2, name: "About", href: "/about" },
  { id: 3, name: "Services", href: "/services" },
  { id: 4, name: "Gallery", href: "/gallery" },
  { id: 5, name: "Testimonials", href: "/testimonials" },
  { id: 6, name: "Contact", href: "/contact" },
];

// HomePage Services Cards Data
export const HomePageService = [
  {
    icon: Home,
    title: "Installation",
    description: "Professional tile installation for floors and walls",
  },
  {
    icon: Hammer,
    title: "Renovation",
    description: "Complete bathroom and kitchen renovations",
  },
  {
    icon: Wrench,
    title: "Outdoor",
    description: "Terrace and outdoor tiling solutions",
  },
  {
    icon: CheckCircle,
    title: "Repairs",
    description: "Quick and reliable repair services",
  },
];
