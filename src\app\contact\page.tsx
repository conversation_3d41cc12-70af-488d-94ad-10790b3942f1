"use client";

import { useState } from "react";
import { Phone, Mail, MapPin, Clock, MessageCircle, Send } from "lucide-react";
import SectionHeader from "@/components/shared/section-header";
import Footer from "@/components/shared/footer";
import { Button } from "@/components/ui/button";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    service: "",
    message: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log("Form submitted:", formData);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const contactInfo = [
    {
      icon: Phone,
      title: "Phone",
      details: ["+49 ************", "Available 24/7 for emergencies"],
      action: "Call Now",
    },
    {
      icon: Mail,
      title: "Email",
      details: ["<EMAIL>", "Response within 24 hours"],
      action: "Send Email",
    },
    {
      icon: MessageCircle,
      title: "WhatsApp",
      details: ["+49 ************", "Instant messaging available"],
      action: "Chat Now",
    },
    {
      icon: MapPin,
      title: "Service Area",
      details: ["Berlin & Brandenburg", "Free quotes within 50km"],
      action: "Get Directions",
    },
  ];

  const businessHours = [
    { day: "Monday - Friday", hours: "8:00 AM - 6:00 PM" },
    { day: "Saturday", hours: "9:00 AM - 4:00 PM" },
    { day: "Sunday", hours: "Emergency calls only" },
  ];

  return (
    <div className="min-h-screen bg-[var(--primary-bg)] pt-16">
      {/* Hero Section */}
      <section className="gradient-hero px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <SectionHeader
            subtitle="Get In Touch"
            title="Contact Alexis Tiling"
            description="Ready to transform your space? Get in touch for a free consultation and quote. We're here to help bring your tiling vision to life."
          />
        </div>
      </section>

      {/* Contact Info Cards */}
      <section className="bg-[var(--card-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {contactInfo.map((info, index) => (
              <div
                key={index}
                className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 text-center shadow-sm"
              >
                <info.icon className="mx-auto mb-4 h-12 w-12 text-[var(--secondary-brand)]" />
                <h3 className="mb-3 text-lg font-semibold text-[var(--primary-text)]">
                  {info.title}
                </h3>
                <div className="mb-4 space-y-1">
                  {info.details.map((detail, idx) => (
                    <p
                      key={idx}
                      className={`${idx === 0 ? "font-medium text-[var(--primary-text)]" : "text-sm text-[var(--secondary-text)]"}`}
                    >
                      {detail}
                    </p>
                  ))}
                </div>
                <Button className="w-full bg-[var(--secondary-brand)] text-white hover:bg-[var(--secondary-brand)]/90">
                  {info.action}
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Business Hours */}
      <section className="bg-[var(--secondary-bg)] px-4 py-16 sm:px-6 lg:px-8 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-3">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 shadow-sm">
                <h2 className="mb-6 text-2xl font-bold text-[var(--primary-text)]">
                  Send Us a Message
                </h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-[var(--primary-text)]"
                      >
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full rounded-lg border border-[var(--border-color)] bg-white px-4 py-3 text-[var(--primary-text)] focus:border-[var(--secondary-brand)] focus:ring-2 focus:ring-[var(--secondary-brand)]/20 focus:outline-none"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="email"
                        className="mb-2 block text-sm font-medium text-[var(--primary-text)]"
                      >
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full rounded-lg border border-[var(--border-color)] bg-white px-4 py-3 text-[var(--primary-text)] focus:border-[var(--secondary-brand)] focus:ring-2 focus:ring-[var(--secondary-brand)]/20 focus:outline-none"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <label
                        htmlFor="phone"
                        className="mb-2 block text-sm font-medium text-[var(--primary-text)]"
                      >
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full rounded-lg border border-[var(--border-color)] bg-white px-4 py-3 text-[var(--primary-text)] focus:border-[var(--secondary-brand)] focus:ring-2 focus:ring-[var(--secondary-brand)]/20 focus:outline-none"
                        placeholder="+49 ************"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="service"
                        className="mb-2 block text-sm font-medium text-[var(--primary-text)]"
                      >
                        Service Needed
                      </label>
                      <select
                        id="service"
                        name="service"
                        value={formData.service}
                        onChange={handleChange}
                        className="w-full rounded-lg border border-[var(--border-color)] bg-white px-4 py-3 text-[var(--primary-text)] focus:border-[var(--secondary-brand)] focus:ring-2 focus:ring-[var(--secondary-brand)]/20 focus:outline-none"
                      >
                        <option value="">Select a service</option>
                        <option value="bathroom">Bathroom Tiling</option>
                        <option value="kitchen">Kitchen Tiling</option>
                        <option value="floor">Floor Tiling</option>
                        <option value="commercial">Commercial Tiling</option>
                        <option value="outdoor">Outdoor Tiling</option>
                        <option value="repair">Tile Repairs</option>
                        <option value="consultation">Consultation Only</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="mb-2 block text-sm font-medium text-[var(--primary-text)]"
                    >
                      Project Details *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      required
                      rows={5}
                      value={formData.message}
                      onChange={handleChange}
                      className="w-full rounded-lg border border-[var(--border-color)] bg-white px-4 py-3 text-[var(--primary-text)] focus:border-[var(--secondary-brand)] focus:ring-2 focus:ring-[var(--secondary-brand)]/20 focus:outline-none"
                      placeholder="Please describe your project, including room size, preferred materials, timeline, and any specific requirements..."
                    />
                  </div>

                  <Button
                    type="submit"
                    className="flex w-full items-center justify-center gap-2 bg-[var(--secondary-brand)] text-white hover:bg-[var(--secondary-brand)]/90 md:w-auto"
                  >
                    <Send className="h-4 w-4" />
                    Send Message
                  </Button>
                </form>
              </div>
            </div>

            {/* Business Hours & Additional Info */}
            <div className="space-y-6">
              {/* Business Hours */}
              <div className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 shadow-sm">
                <div className="mb-4 flex items-center space-x-2">
                  <Clock className="h-6 w-6 text-[var(--secondary-brand)]" />
                  <h3 className="text-lg font-semibold text-[var(--primary-text)]">
                    Business Hours
                  </h3>
                </div>
                <div className="space-y-3">
                  {businessHours.map((schedule, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="text-[var(--secondary-text)]">
                        {schedule.day}
                      </span>
                      <span className="font-medium text-[var(--primary-text)]">
                        {schedule.hours}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Emergency Contact */}
              <div className="rounded-xl border border-[var(--border-color)] bg-gradient-to-r from-[var(--secondary-brand)]/10 to-[var(--tertiary-brand)]/10 p-6 shadow-sm">
                <h3 className="mb-3 text-lg font-semibold text-[var(--primary-text)]">
                  Emergency Service
                </h3>
                <p className="mb-4 text-sm text-[var(--secondary-text)]">
                  Need urgent tiling repairs? We offer 24/7 emergency services
                  for water damage and urgent repairs.
                </p>
                <Button className="w-full bg-[var(--secondary-brand)] text-white hover:bg-[var(--secondary-brand)]/90">
                  Emergency Contact
                </Button>
              </div>

              {/* Service Area */}
              <div className="rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 shadow-sm">
                <h3 className="mb-3 text-lg font-semibold text-[var(--primary-text)]">
                  Service Area
                </h3>
                <p className="mb-3 text-sm text-[var(--secondary-text)]">
                  We proudly serve Berlin and Brandenburg with free quotes
                  within 50km of Berlin city center.
                </p>
                <ul className="space-y-1 text-sm text-[var(--secondary-text)]">
                  <li>• Berlin (all districts)</li>
                  <li>• Brandenburg an der Havel</li>
                  <li>• Potsdam</li>
                  <li>• Surrounding areas</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
