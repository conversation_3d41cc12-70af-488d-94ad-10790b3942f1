import { But<PERSON> } from "@/components/ui/button";
import { Phone, MessageCircle, ArrowRight } from "lucide-react";
import Link from "next/link";

interface CTASectionProps {
  title?: string;
  description?: string;
  showButtons?: boolean;
  className?: string;
}

const CTASection = ({ 
  title = "Ready to Transform Your Space?",
  description = "Get a free quote today and let our experts bring your vision to life with professional tiling services.",
  showButtons = true,
  className = ""
}: CTASectionProps) => {
  return (
    <section className={`gradient-secondary ${className}`}>
      <div className="container-custom section-padding">
        <div className="text-center text-white">
          <h2 className="mb-4 text-3xl font-bold sm:text-4xl lg:text-5xl">
            {title}
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-lg opacity-90 sm:text-xl">
            {description}
          </p>
          
          {showButtons && (
            <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
              <Button
                size="lg"
                className="flex items-center gap-2 bg-white text-[var(--primary-text)] hover:bg-gray-100"
              >
                <Phone className="h-5 w-5" />
                Call Now - Free Quote
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="flex items-center gap-2 border-white text-white hover:bg-white hover:text-[var(--primary-text)]"
              >
                <MessageCircle className="h-5 w-5" />
                WhatsApp Chat
              </Button>
              
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="ghost"
                  className="flex items-center gap-2 text-white hover:bg-white/10"
                >
                  Contact Us
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default CTASection;
