"use client";

import { useState } from "react";
import { Filter } from "lucide-react";
import SectionHeader from "@/components/shared/section-header";
import CTASection from "@/components/shared/cta-section";
import Footer from "@/components/shared/footer";
import { Button } from "@/components/ui/button";

export default function GalleryPage() {
  const [activeFilter, setActiveFilter] = useState("All");

  const filters = ["All", "Bathroom", "Kitchen", "Commercial", "Outdoor", "Residential"];

  const projects = [
    {
      id: 1,
      title: "Modern Bathroom Renovation",
      category: "Bathroom",
      location: "Berlin Mitte",
      image: "/hero.jpg",
      description: "Complete bathroom transformation with premium porcelain tiles"
    },
    {
      id: 2,
      title: "Kitchen Backsplash Design",
      category: "Kitchen",
      location: "Brandenburg",
      image: "/hero.jpg",
      description: "Elegant subway tile backsplash with custom pattern"
    },
    {
      id: 3,
      title: "Outdoor Terrace Tiling",
      category: "Outdoor",
      location: "Berlin Charlottenburg",
      image: "/hero.jpg",
      description: "Weather-resistant outdoor tiles for terrace renovation"
    },
    {
      id: 4,
      title: "Commercial Floor Installation",
      category: "Commercial",
      location: "Berlin Kreuzberg",
      image: "/hero.jpg",
      description: "High-traffic commercial flooring for restaurant"
    },
    {
      id: 5,
      title: "Luxury Hotel Bathroom",
      category: "Commercial",
      location: "Berlin Prenzlauer Berg",
      image: "/hero.jpg",
      description: "Premium marble tiles for luxury hotel renovation"
    },
    {
      id: 6,
      title: "Residential Floor Tiling",
      category: "Residential",
      location: "Brandenburg",
      image: "/hero.jpg",
      description: "Large format tiles for modern living space"
    },
    {
      id: 7,
      title: "Master Bathroom Suite",
      category: "Bathroom",
      location: "Berlin Wilmersdorf",
      image: "/hero.jpg",
      description: "Spa-like bathroom with natural stone tiles"
    },
    {
      id: 8,
      title: "Contemporary Kitchen",
      category: "Kitchen",
      location: "Berlin Friedrichshain",
      image: "/hero.jpg",
      description: "Sleek kitchen with geometric tile patterns"
    },
    {
      id: 9,
      title: "Office Reception Area",
      category: "Commercial",
      location: "Berlin Mitte",
      image: "/hero.jpg",
      description: "Professional office space with designer tiles"
    }
  ];

  const filteredProjects = activeFilter === "All" 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <div className="min-h-screen bg-[var(--primary-bg)] pt-16">
      {/* Hero Section */}
      <section className="section-padding gradient-hero">
        <div className="container-custom">
          <SectionHeader
            subtitle="Our Portfolio"
            title="Project Gallery"
            description="Explore our collection of completed tiling projects across Berlin and Brandenburg. Each project showcases our commitment to quality and attention to detail."
          />
        </div>
      </section>

      {/* Filter Section */}
      <section className="bg-[var(--card-bg)] py-8">
        <div className="container-custom">
          <div className="flex flex-wrap items-center justify-center gap-4">
            <Filter className="h-5 w-5 text-[var(--secondary-text)]" />
            {filters.map((filter) => (
              <Button
                key={filter}
                variant={activeFilter === filter ? "default" : "outline"}
                className={`${
                  activeFilter === filter
                    ? "btn-secondary"
                    : "border-[var(--border-color)] text-[var(--secondary-text)] hover:bg-[var(--hover-bg)]"
                }`}
                onClick={() => setActiveFilter(filter)}
              >
                {filter}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="section-padding bg-[var(--secondary-bg)]">
        <div className="container-custom">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {filteredProjects.map((project) => (
              <div key={project.id} className="project-card group">
                <div className="relative overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="h-64 w-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-4 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100">
                    <span className="mb-2 inline-block rounded-full bg-[var(--secondary-brand)] px-3 py-1 text-xs font-medium">
                      {project.category}
                    </span>
                    <h3 className="mb-1 text-lg font-semibold">{project.title}</h3>
                    <p className="mb-1 text-sm opacity-90">{project.location}</p>
                    <p className="text-xs opacity-75">{project.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <p className="text-[var(--secondary-text)]">
                No projects found for the selected category.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Before & After Section */}
      <section className="section-padding bg-[var(--card-bg)]">
        <div className="container-custom">
          <SectionHeader
            title="Before & After"
            description="See the dramatic transformations we've achieved for our clients."
            className="mb-12"
          />
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2">
            <div className="space-y-6">
              <h3 className="text-2xl font-semibold text-[var(--primary-text)]">
                Bathroom Transformation
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="mb-2 text-sm font-medium text-[var(--secondary-text)]">Before</p>
                  <img
                    src="/hero.jpg"
                    alt="Before renovation"
                    className="rounded-lg shadow-md"
                  />
                </div>
                <div>
                  <p className="mb-2 text-sm font-medium text-[var(--secondary-text)]">After</p>
                  <img
                    src="/hero.jpg"
                    alt="After renovation"
                    className="rounded-lg shadow-md"
                  />
                </div>
              </div>
              <p className="text-[var(--secondary-text)]">
                Complete bathroom renovation featuring waterproof porcelain tiles, 
                custom shower niche, and heated flooring system.
              </p>
            </div>
            <div className="space-y-6">
              <h3 className="text-2xl font-semibold text-[var(--primary-text)]">
                Kitchen Backsplash
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="mb-2 text-sm font-medium text-[var(--secondary-text)]">Before</p>
                  <img
                    src="/hero.jpg"
                    alt="Before renovation"
                    className="rounded-lg shadow-md"
                  />
                </div>
                <div>
                  <p className="mb-2 text-sm font-medium text-[var(--secondary-text)]">After</p>
                  <img
                    src="/hero.jpg"
                    alt="After renovation"
                    className="rounded-lg shadow-md"
                  />
                </div>
              </div>
              <p className="text-[var(--secondary-text)]">
                Modern kitchen backsplash with subway tiles in herringbone pattern, 
                creating a stunning focal point in this contemporary kitchen.
              </p>
            </div>
          </div>
        </div>
      </section>

      <CTASection />
      <Footer />
    </div>
  );
}
