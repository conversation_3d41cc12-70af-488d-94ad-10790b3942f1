import { <PERSON><PERSON><PERSON>, MessageCircle, Star } from "lucide-react";
import Link from "next/link";

export const Hero = () => {
  return (
    <section className="relative py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <div className="space-y-8">
            <div className="space-y-1">
              <p className="hover:bg-secondary/80 w-fit rounded-lg border-transparent bg-[var(--primary-brand)] px-2 text-neutral-50">
                Professional Tiling Services
              </p>
              <h1 className="text-4xl leading-tight font-bold lg:text-6xl">
                High-Quality Tiling in{" "}
                <span className="text-[var(--secondary-brand)]">
                  Berlin & Brandenburg
                </span>
              </h1>
              <p className="text-muted-foreground text-xl leading-relaxed">
                Reliable craftsmanship for bathrooms, kitchens, terraces, and
                more. Transform your space with our professional tiling
                expertise.
              </p>
            </div>

            <div className="flex flex-col items-center gap-4 sm:flex-row md:items-start">
              <Link href="/contact">
                <button className="hover:bg-primary/90 flex h-11 w-fit cursor-pointer items-center rounded-md bg-[var(--secondary-brand)] px-8 text-neutral-50 sm:w-auto">
                  Get a Quote
                  <ArrowRight className="ml-2 h-5 w-5" />
                </button>
              </Link>
              <button className="flex h-11 w-fit cursor-pointer items-center rounded-md border-2 border-[var(--secondary-brand)] bg-[var(--primary-bg)] px-8 hover:bg-[var(--primary-card)] hover:text-[var(--primary-brand)] sm:w-auto">
                <MessageCircle className="mr-2 h-5 w-5" />
                WhatsApp
              </button>
            </div>

            <div className="flex items-center gap-6 pt-4">
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="fill-primary text-primary h-5 w-5"
                    />
                  ))}
                </div>
                <span className="text-muted-foreground text-sm">
                  5.0 rating
                </span>
              </div>
              <div className="text-muted-foreground text-sm">
                50+ completed projects
              </div>
            </div>
          </div>

          <div className="relative">
            <img
              src="/hero.jpg"
              alt="Professional tiling work by Alexis"
              className="h-[400px] w-full rounded-2xl object-cover shadow-2xl lg:h-[500px]"
            />
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>
        </div>
      </div>
    </section>
  );
};
