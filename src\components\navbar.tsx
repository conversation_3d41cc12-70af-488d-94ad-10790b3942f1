"use client";
import { NavMenuData } from "@/app/constant";
import Link from "next/link";
import { useState } from "react";
import { Button } from "./ui/button";
import { usePathname } from "next/navigation";
import { Phone, MessageCircle, X, Menu } from "lucide-react";
import { motion, AnimatePresence } from "motion/react";

const NavBar = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<number | null>(null);

  const pathname = usePathname();

  const isActive = (href: string) => {
    return pathname === href; // exact match
  };

  return (
    <>
      <div className={` ${isOpen ? "hidden" : "block"} `}>
        <nav className="fixed top-0 right-0 left-0 z-50 border-b border-gray-200/30 bg-white/95 shadow-sm backdrop-blur-sm">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              {/* Logo */}
              <Link href="/" className="text-neutral-850 text-xl font-bold">
                alexis
                <span className="text-[var(--secondary-brand)]">tiling </span>
              </Link>

              {/* Desktop Navigation */}
              <div className="hidden items-center md:flex">
                {NavMenuData.map((item) => (
                  <Link
                    key={item.id}
                    href={item.href}
                    onMouseEnter={() => setIsHovered(item.id)}
                    onMouseLeave={() => setIsHovered(null)}
                    className={`relative flex cursor-pointer items-center space-x-2 px-5 py-1 font-medium transition-all duration-300 hover:text-white ${
                      isActive(item.href)
                        ? "rounded-lg bg-[var(--secondary-brand)] font-semibold text-white"
                        : "text-gray-700"
                    }`}
                  >
                    {isHovered === item.id && (
                      <motion.span
                        layoutId="hover-span"
                        className="absolute inset-0 w-full rounded-lg bg-[var(--primary-brand)] shadow-sm"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                      />
                    )}
                    <span className="relative z-10">{item.name}</span>
                  </Link>
                ))}
              </div>

              {/* Desktop CTA Buttons */}
              <div className="hidden items-center space-x-4 md:flex">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 border-purple-200 text-[#183e7c] shadow-sm transition-all duration-300 hover:border-purple-300 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:shadow-md dark:border-purple-700 dark:text-purple-300 dark:hover:bg-gradient-to-r dark:hover:from-purple-900/20 dark:hover:to-pink-900/20"
                >
                  <Phone className="h-4 w-4" />
                  <span className="hidden text-[var(--secondary-brand)] lg:inline">
                    Call Now
                  </span>
                </Button>

                <Button
                  size="sm"
                  className="flex transform items-center gap-2 bg-[var(--secondary-brand)] text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
                >
                  <MessageCircle className="h-4 w-4" />
                  <span className="hidden lg:inline">WhatsApp</span>
                </Button>
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(!isOpen)}
                  className="rounded-xl p-2.5 transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 dark:hover:from-purple-900/20 dark:hover:to-pink-900/20"
                >
                  <motion.div
                    animate={
                      isOpen
                        ? { rotate: 180, scale: 1.1 }
                        : { rotate: 0, scale: 1 }
                    }
                    transition={{ duration: 0.3, type: "spring" }}
                  >
                    {isOpen ? (
                      <X className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    ) : (
                      <Menu className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                    )}
                  </motion.div>
                </Button>
              </div>
            </div>
          </div>
        </nav>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Dark Backdrop Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="fixed inset-0 z-40 bg-gradient-to-b from-black/60 via-black/70 to-black/80 backdrop-blur-md md:hidden"
              onClick={() => setIsOpen(false)}
            />

            {/* Bottom Slide-up Menu */}
            <motion.div
              initial={{ y: "100%", opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: "100%", opacity: 0 }}
              transition={{
                type: "spring",
                damping: 30,
                stiffness: 300,
                opacity: { duration: 0.3 },
              }}
              className="fixed right-0 bottom-0 left-0 z-50 max-h-[90vh] overflow-hidden rounded-t-3xl bg-white/98 shadow-2xl backdrop-blur-xl md:hidden"
            >
              <div className="relative">
                {/* Drag Handle */}
                <div className="flex justify-center pt-4 pb-2">
                  <div className="h-1.5 w-12 rounded-full bg-gray-300"></div>
                </div>

                {/* Header */}
                <div className="flex items-center justify-between border-b border-gray-100 px-6 py-4">
                  <Link
                    href="/"
                    className="bg-[#10b77f] bg-clip-text text-xl font-bold text-transparent"
                    onClick={() => setIsOpen(false)}
                  >
                    Alexis Tiling
                  </Link>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="rounded-xl p-2 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 dark:hover:from-purple-900/20 dark:hover:to-pink-900/20"
                  >
                    <X className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </Button>
                </div>

                {/* Navigation Content */}
                <div className="max-h-[calc(90vh-120px)] overflow-y-auto px-6">
                  {/* Navigation Links */}
                  <div className="mb-2 space-y-2">
                    {NavMenuData.map((item, index) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{
                          delay: index * 0.1 + 0.2,
                          type: "spring",
                          damping: 25,
                          stiffness: 200,
                        }}
                      >
                        <Link
                          href={item.href}
                          className={`group block rounded-xl px-5 py-3 text-base font-medium transition-all duration-300 ${
                            isActive(item.href)
                              ? "scale-[1.02] transform bg-[var(--secondary-brand)] text-white shadow-lg"
                              : "text-gray-700"
                          }`}
                          onClick={() => setIsOpen(false)}
                        >
                          <div className="flex items-center justify-between">
                            <span className="relative z-10">{item.name}</span>
                          </div>
                        </Link>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Buttons */}
                  <motion.div
                    className="space-y-4"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      delay: 0.5,
                      type: "spring",
                      damping: 25,
                      stiffness: 200,
                    }}
                  >
                    <Button
                      variant="outline"
                      className="h-12 w-full transform justify-center gap-3 rounded-xl border-2 border-purple-200 font-medium text-neutral-950 shadow-sm"
                      onClick={() => setIsOpen(false)}
                    >
                      <Phone className="h-5 w-5" />
                      Call Now - Get Free Quote
                    </Button>

                    <Button
                      className="h-12 w-full transform justify-center gap-3 rounded-xl bg-[var(--tertiary-brand)] font-medium text-white shadow-lg"
                      onClick={() => setIsOpen(false)}
                    >
                      <MessageCircle className="h-5 w-5" />
                      WhatsApp - Instant Chat
                    </Button>
                  </motion.div>

                  {/* Contact Info */}
                  <motion.div
                    className="mt-8 rounded-2xl border border-purple-100 bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50 p-4 dark:border-purple-800 dark:from-purple-900/10 dark:via-pink-900/10 dark:to-blue-900/10"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      delay: 0.6,
                      type: "spring",
                      damping: 25,
                      stiffness: 200,
                    }}
                  >
                    <p className="mb-1 text-center text-sm font-medium text-purple-700 dark:text-purple-300">
                      🏆 Professional Tiling Services
                    </p>
                    <p className="text-center text-xs text-gray-600 dark:text-gray-400">
                      Trusted by 1000+ happy customers
                    </p>
                  </motion.div>
                </div>

                {/* Bottom Safe Area */}
                <div className="pointer-events-none h-6 bg-gradient-to-t from-white/50 to-transparent dark:from-gray-900/50"></div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default NavBar;
