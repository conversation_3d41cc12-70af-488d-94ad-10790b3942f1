import { Star, Quote } from "lucide-react";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import SectionHeader from "@/components/shared/section-header";

export const TestimonialsPreview = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      location: "Berlin",
      rating: 5,
      text: "Exceptional work! <PERSON> transformed our bathroom completely. The attention to detail and quality of work exceeded our expectations. Highly recommended!",
      project: "Bathroom Renovation"
    },
    {
      id: 2,
      name: "<PERSON>",
      location: "Brandenburg",
      rating: 5,
      text: "Professional, punctual, and precise. The kitchen backsplash looks amazing and the cleanup was thorough. Will definitely hire again for future projects.",
      project: "Kitchen Backsplash"
    },
    {
      id: 3,
      name: "<PERSON>",
      location: "Berlin",
      rating: 5,
      text: "Outstanding service from start to finish. Great communication, fair pricing, and beautiful results. Our terrace looks incredible!",
      project: "Outdoor Terrace"
    }
  ];

  return (
    <section className="section-padding bg-[var(--card-bg)]">
      <div className="container-custom">
        <SectionHeader
          subtitle="Client Reviews"
          title="What Our Customers Say"
          description="Don't just take our word for it. Here's what our satisfied customers have to say about our tiling services."
          className="mb-12"
        />

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="testimonial-card">
              <div className="mb-4 flex items-center justify-between">
                <div className="flex">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <Quote className="h-6 w-6 text-[var(--secondary-brand)] opacity-50" />
              </div>
              
              <p className="mb-4 text-[var(--secondary-text)] italic">
                "{testimonial.text}"
              </p>
              
              <div className="border-t border-[var(--border-color)] pt-4">
                <div className="font-semibold text-[var(--primary-text)]">
                  {testimonial.name}
                </div>
                <div className="text-sm text-[var(--muted-text)]">
                  {testimonial.location} • {testimonial.project}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link href="/testimonials">
            <Button variant="outline" className="border-[var(--secondary-brand)] text-[var(--secondary-brand)] hover:bg-[var(--secondary-brand)] hover:text-white">
              Read More Reviews
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};
