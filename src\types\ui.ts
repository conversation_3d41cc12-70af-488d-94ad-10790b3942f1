// Types Of Custom Buttons Props Types
export type ButtonVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "ghost"
  | "link";

//   Types of Custom Buttons Props Types
export type ButtonSize = "default" | "sm" | "lg" | "icon";

// * Custom Buttons Props Type
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
}

