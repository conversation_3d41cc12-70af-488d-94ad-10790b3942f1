@import "tailwindcss";

@theme inline {
  --primary-bg: #f9f9f6;
  --primary-text: #2e2e2e;
  --primary-card: #f3eae2;
  --card-hover: #f1f3f5;
  --secondary-card: #e8e9f0;

  --primary-brand: #3840d4;
  --secondary-brand: #10b77f;
  --tertiary-brand: #f67e5a;
}

.card {
  padding: 2em;
}

/* Service cards */
/* .service-card {
    @apply bg-card rounded-xl border p-6 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  } */

/* Project gallery cards */
/* .project-card {
    @apply bg-card overflow-hidden rounded-xl shadow-md transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
  } */

/* Testimonial cards */
/* .testimonial-card {
    @apply bg-card border-l-primary rounded-xl border-l-4 p-6 shadow-sm transition-all duration-300 hover:shadow-md;
  } */

/* Premium gradients */
/* .gradient-primary {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)),
      hsl(var(--primary-hover))
    ); */

/* .gradient-hero {
    background: linear-gradient(
      135deg,
      hsl(var(--background)) 0%,
      hsl(var(--muted)) 100%
    );
  } */
