@import "tailwindcss";


@theme inline {
  /* Background Colors */
  --primary-bg: #f8fafc;
  --secondary-bg: #f1f5f9;
  --card-bg: #ffffff;
  --muted-bg: #e2e8f0;

  /* Text Colors */
  --primary-text: #1e293b;
  --secondary-text: #475569;
  --muted-text: #64748b;
  --light-text: #94a3b8;

  /* Brand Colors */
  --primary-brand: #3840d4;
  --secondary-brand: #10b77f;
  --tertiary-brand: #f67e5a;
  --accent-brand: #8b5cf6;

  /* Utility Colors */
  --border-color: #e2e8f0;
  --hover-bg: #f1f5f9;
  --shadow-light: rgba(15, 23, 42, 0.08);
  --shadow-medium: rgba(15, 23, 42, 0.12);
  --shadow-heavy: rgba(15, 23, 42, 0.16);
}

/* Base Styles */
* {
  scroll-behavior: smooth;
}

body {
  background-color: var(--primary-bg);
  color: var(--primary-text);
}

/* Utility Classes */
.section-padding {
  @apply px-4 py-16 sm:px-6 lg:px-8 lg:py-20;
}

.container-custom {
  @apply mx-auto max-w-7xl;
}

.text-gradient {
  background: linear-gradient(
    135deg,
    var(--primary-brand),
    var(--secondary-brand)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card Styles */
.card-base {
  @apply rounded-xl border border-[var(--border-color)] bg-[var(--card-bg)] p-6 shadow-sm transition-all duration-300;
}

.card-hover {
  @apply hover:-translate-y-1 hover:shadow-lg;
}

.service-card {
  @apply card-base card-hover;
}

.project-card {
  @apply card-base card-hover overflow-hidden p-0;
}

.testimonial-card {
  @apply card-base border-l-4 border-l-[var(--secondary-brand)] hover:shadow-md;
}

/* Button Styles */
.btn-primary {
  @apply bg-[var(--primary-brand)] text-white hover:bg-[var(--primary-brand)]/90 focus:ring-[var(--primary-brand)]/50;
}

.btn-secondary {
  @apply bg-[var(--secondary-brand)] text-white hover:bg-[var(--secondary-brand)]/90 focus:ring-[var(--secondary-brand)]/50;
}

/* Gradient Backgrounds */
.gradient-hero {
  background: linear-gradient(
    135deg,
    var(--primary-bg) 0%,
    var(--secondary-bg) 100%
  );
}

.gradient-primary {
  background: linear-gradient(
    135deg,
    var(--primary-brand),
    var(--accent-brand)
  );
}

.gradient-secondary {
  background: linear-gradient(
    135deg,
    var(--secondary-brand),
    var(--tertiary-brand)
  );
}

/* Section Styles */
.section-title {
  @apply text-3xl font-bold text-[var(--primary-text)] sm:text-4xl lg:text-5xl;
}

.section-subtitle {
  @apply text-lg text-[var(--secondary-text)] sm:text-xl;
}

/* Animation Classes */
.fade-in {
  @apply translate-y-8 opacity-0 transition-all duration-700 ease-out;
}

.fade-in.visible {
  @apply translate-y-0 opacity-100;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-brand);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-brand);
}
