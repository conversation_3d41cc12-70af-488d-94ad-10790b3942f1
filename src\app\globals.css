@import "tailwindcss";

@theme inline {
  /* Background Colors */
  --primary-bg: #f8fafc;
  --secondary-bg: #f1f5f9;
  --card-bg: #ffffff;
  --muted-bg: #e2e8f0;

  /* Text Colors */
  --primary-text: #1e293b;
  --secondary-text: #475569;
  --muted-text: #64748b;
  --light-text: #94a3b8;

  /* Brand Colors */
  --primary-brand: #3840d4;
  --secondary-brand: #10b77f;
  --tertiary-brand: #f67e5a;
  --accent-brand: #8b5cf6;

  /* Utility Colors */
  --border-color: #e2e8f0;
  --hover-bg: #f1f5f9;
  --shadow-light: rgba(15, 23, 42, 0.08);
  --shadow-medium: rgba(15, 23, 42, 0.12);
  --shadow-heavy: rgba(15, 23, 42, 0.16);
}

/* Base Styles */
* {
  scroll-behavior: smooth;
}

body {
  background-color: var(--primary-bg);
  color: var(--primary-text);
}

/* Gradient Backgrounds */
.gradient-hero {
  background: linear-gradient(
    135deg,
    var(--primary-bg) 0%,
    var(--secondary-bg) 100%
  );
}

.gradient-secondary {
  background: linear-gradient(
    135deg,
    var(--secondary-brand),
    var(--tertiary-brand)
  );
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-brand);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-brand);
}
