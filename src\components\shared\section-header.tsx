interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  centered?: boolean;
  className?: string;
}

const SectionHeader = ({
  title,
  subtitle,
  description,
  centered = true,
  className = "",
}: SectionHeaderProps) => {
  return (
    <div className={`${centered ? "text-center" : ""} ${className}`}>
      {subtitle && (
        <p className="mb-2 text-sm font-semibold tracking-wider text-[var(--secondary-brand)] uppercase">
          {subtitle}
        </p>
      )}
      <h2 className="mb-4 text-3xl font-bold text-[var(--primary-text)] sm:text-4xl lg:text-5xl">
        {title}
      </h2>
      {description && (
        <p className="mx-auto max-w-3xl text-lg text-[var(--secondary-text)] sm:text-xl">
          {description}
        </p>
      )}
    </div>
  );
};

export default SectionHeader;
