interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  centered?: boolean;
  className?: string;
}

const SectionHeader = ({ 
  title, 
  subtitle, 
  description, 
  centered = true, 
  className = "" 
}: SectionHeaderProps) => {
  return (
    <div className={`${centered ? "text-center" : ""} ${className}`}>
      {subtitle && (
        <p className="mb-2 text-sm font-semibold uppercase tracking-wider text-[var(--secondary-brand)]">
          {subtitle}
        </p>
      )}
      <h2 className="section-title mb-4">
        {title}
      </h2>
      {description && (
        <p className="section-subtitle mx-auto max-w-3xl">
          {description}
        </p>
      )}
    </div>
  );
};

export default SectionHeader;
