import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import NavBar from "@/components/navbar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Alexis Tiling - Professional Tiling Services in Berlin & Brandenburg",
  description:
    "Expert tiling services for bathrooms, kitchens, and commercial spaces in Berlin and Brandenburg. Quality craftsmanship, competitive prices, and 100% satisfaction guarantee.",
  keywords:
    "tiling, tiles, bathroom renovation, kitchen backsplash, Berlin, Brandenburg, professional tiler",
  authors: [{ name: "<PERSON> Tiling" }],
  openGraph: {
    title: "<PERSON> - Professional Tiling Services",
    description:
      "Transform your space with expert tiling services in Berlin & Brandenburg",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NavBar />
        {children}
      </body>
    </html>
  );
}
