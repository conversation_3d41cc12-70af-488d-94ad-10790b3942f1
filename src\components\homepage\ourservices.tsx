import { ArrowRight } from "lucide-react";
import Link from "next/link";

import { HomePageService } from "@/app/constant";

export const OurServices = () => {
  return (
    <section className="bg-muted/30 py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Heading */}
        <div className="mb-12 space-y-4 text-center">
          <h2 className="text-3xl font-bold lg:text-4xl">Our Services</h2>
          <p className="mx-auto max-w-2xl text-xl text-[var(--secondary-brand)]">
            Comprehensive tiling solutions for every need
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {HomePageService.map((service, index) => (
            <Card
              key={index}
              title={service.title}
              description={service.description}
              Icon={service.icon}
            />
          ))}
        </div>

        {/* CTA */}
        <div className="mt-6 flex justify-end text-center">
          <Link href="/services">
            <button className="flex h-11 w-fit items-center rounded-md border border-[var(--secondary-brand)] bg-[var(--primary-bg)] px-8 hover:bg-[var(--primary-card)] sm:w-auto">
              View All Services
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

type CardProps = {
  title: string;
  description: string;
  Icon: React.ElementType;
};

const Card = ({ title, description, Icon }: CardProps) => {
  return (
    <div className="group rounded-xl border border-[var(--secondary-brand)] bg-[var(--primary-card)] p-6 text-center shadow-sm transition hover:shadow-md">
      <Icon className="text-neutral-850 mx-auto mb-4 h-12 w-12 transition-transform duration-300 group-hover:scale-110" />
      <h3 className="mb-2 text-lg font-semibold">{title}</h3>
      <p className="text-muted-foreground text-sm">{description}</p>
    </div>
  );
};
